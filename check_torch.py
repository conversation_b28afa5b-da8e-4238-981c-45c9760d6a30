import torch
import os
print("PyTorch路径:", torch.__file__)
print("PyTorch版本:", torch.__version__)
print("CUDA版本:", torch.version.cuda)
print("CUDA可用:", torch.cuda.is_available())

# 检查torch库的实际位置
torch_dir = os.path.dirname(torch.__file__)
print("Torch目录:", torch_dir)

# 检查是否有CUDA相关的库文件
cuda_libs = []
for root, dirs, files in os.walk(torch_dir):
    for file in files:
        if 'cuda' in file.lower() and file.endswith('.dll'):
            cuda_libs.append(os.path.join(root, file))

print("找到的CUDA库文件数量:", len(cuda_libs))
if cuda_libs:
    print("前5个CUDA库文件:")
    for lib in cuda_libs[:5]:
        print(" ", lib)
